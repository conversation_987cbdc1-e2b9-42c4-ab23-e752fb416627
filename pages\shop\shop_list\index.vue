<template>
	<view class="shop-list-container" :style="colorStyle">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight }">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">店铺街</text>
				</view>
				<view class="navbar-right">
					<text class="iconfont icon-sousuo" @click="showSearch"></text>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-bar" v-if="searchVisible">
			<view class="search-input">
				<text class="iconfont icon-sousuo"></text>
				<input type="text" v-model="searchKeyword" placeholder="搜索店铺名称" @confirm="searchShops" />
			</view>
			<view class="search-cancel" @click="hideSearch">取消</view>
		</view>

		<!-- 分类筛选栏 -->
		<view class="filter-bar" :style="{ top: searchVisible ? '160rpx' : '88rpx' }">
			<scroll-view scroll-x="true" class="filter-scroll">
				<view class="filter-list">
					<view
						class="filter-item"
						:class="{ active: currentFilter === item.value }"
						v-for="item in filterList"
						:key="item.value"
						@click="setFilter(item.value)"
					>
						{{ item.label }}
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 店铺列表 -->
		<scroll-view
			class="shop-scroll"
			:style="{ marginTop: searchVisible ? '232rpx' : '160rpx', height: searchVisible ? 'calc(100vh - 232rpx)' : 'calc(100vh - 160rpx)' }"
			scroll-y="true"
			@scrolltolower="loadMore"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
			<view class="shop-list">
				<view class="shop-item" v-for="shop in shopList" :key="shop.id" @click="goShopDetail(shop.id)">
					<!-- 店铺头部 -->
					<view class="shop-header">
						<view class="shop-logo">
							<image :src="shop.logo || defaultLogo" mode="aspectFill"></image>
						</view>
						<view class="shop-info">
							<view class="shop-name">{{ shop.name }}</view>
							<view class="shop-desc">{{ shop.description || '暂无描述' }}</view>
							<view class="shop-stats">
								<view class="stat-item">
									<text class="stat-label">评分</text>
									<text class="stat-value">{{ shop.rating || '5.0' }}</text>
								</view>
								<view class="stat-item">
									<text class="stat-label">销量</text>
									<text class="stat-value">{{ shop.sales || 0 }}</text>
								</view>
								<view class="stat-item">
									<text class="stat-label">商品</text>
									<text class="stat-value">{{ shop.goods_count || 0 }}</text>
								</view>
							</view>
						</view>
						<view class="shop-follow" @click.stop="toggleFollow(shop)">
							<text class="follow-btn" :class="{ followed: shop.is_followed }">
								{{ shop.is_followed ? '已关注' : '关注' }}
							</text>
						</view>
					</view>

					<!-- 商品预览 -->
					<view class="goods-preview" v-if="shop.goods && shop.goods.length > 0">
						<view class="goods-item" v-for="goods in shop.goods.slice(0, 3)" :key="goods.id">
							<image :src="goods.image" mode="aspectFill"></image>
							<view class="goods-price">¥{{ goods.price }}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="shopList.length > 0">
				<text v-if="loading">加载中...</text>
				<text v-else-if="!hasMore">没有更多店铺了</text>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="!loading && shopList.length === 0">
				<image src="/static/images/def_avatar.png" mode="aspectFit"></image>
				<text>暂无店铺数据</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { getShopList, searchShops, followShop } from '@/api/shop.js';
import colors from "@/mixins/color";

export default {
	mixins: [colors],
	data() {
		return {
			statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px',
			searchVisible: false,
			searchKeyword: '',
			currentFilter: 'hot',
			filterList: [
				{ label: '热门', value: 'hot' },
				{ label: '销量', value: 'sales' },
				{ label: '好评', value: 'rating' },
				{ label: '距离', value: 'distance' },
				{ label: '店铺', value: 'all' }
			],
			shopList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			limit: 10,
			defaultLogo: '/static/images/def_avatar.png'
		};
	},
	onLoad() {
		this.loadShopList();
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 显示搜索
		showSearch() {
			this.searchVisible = true;
		},

		// 隐藏搜索
		hideSearch() {
			this.searchVisible = false;
			this.searchKeyword = '';
		},

		// 设置筛选条件
		setFilter(value) {
			this.currentFilter = value;
			this.refreshList();
		},

		// 搜索店铺
		searchShops() {
			this.refreshList();
		},

		// 加载店铺列表
		async loadShopList(refresh = false) {
			if (this.loading) return;
			
			this.loading = true;
			
			if (refresh) {
				this.page = 1;
				this.shopList = [];
				this.hasMore = true;
			}

			try {
				const params = {
					page: this.page,
					limit: this.limit,
					sort: this.currentFilter,
					keyword: this.searchKeyword
				};

				const res = await getShopList(params);
				const newList = res.data.list || [];
				
				if (refresh) {
					this.shopList = newList;
				} else {
					this.shopList.push(...newList);
				}
				
				this.hasMore = newList.length >= this.limit;
				this.page++;
			} catch (error) {
				// 使用模拟数据
				const mockData = this.getMockShopData();
				if (refresh) {
					this.shopList = mockData;
				} else {
					this.shopList.push(...mockData);
				}
				this.hasMore = false;
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 刷新列表
		refreshList() {
			this.loadShopList(true);
		},

		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			this.refreshList();
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.loadShopList();
			}
		},

		// 跳转店铺详情
		goShopDetail(shopId) {
			uni.navigateTo({
				url: `/pages/shop/shop_detail/index?id=${shopId}`
			});
		},

		// 关注/取消关注
		async toggleFollow(shop) {
			try {
				await followShop(shop.id);
				shop.is_followed = !shop.is_followed;
				this.$util.Tips({
					title: shop.is_followed ? '关注成功' : '取消关注成功'
				});
			} catch (error) {
				this.$util.Tips({
					title: error.message || '操作失败'
				});
			}
		},

		// 获取模拟数据
		getMockShopData() {
			return [
				{
					id: 1,
					name: '熊猫百货',
					logo: '/static/images/def_avatar.png',
					description: '37人关注',
					rating: '4.8',
					sales: 1234,
					goods_count: 156,
					is_followed: false,
					goods: [
						{ id: 1, image: '/static/images/1-001.png', price: '99.00' },
						{ id: 2, image: '/static/images/1-002.png', price: '134.00' },
						{ id: 3, image: '/static/images/2-001.png', price: '0' }
					]
				},
				{
					id: 2,
					name: '时尚优品',
					logo: '/static/images/def_avatar.png',
					description: '26人关注',
					rating: '4.9',
					sales: 856,
					goods_count: 89,
					is_followed: true,
					goods: [
						{ id: 4, image: '/static/images/2-002.png', price: '639.00' },
						{ id: 5, image: '/static/images/3-001.png', price: '69.00' },
						{ id: 6, image: '/static/images/3-002.png', price: '0' }
					]
				},
				{
					id: 3,
					name: '时尚时刻精品店',
					logo: '/static/images/def_avatar.png',
					description: '26人关注',
					rating: '4.7',
					sales: 2156,
					goods_count: 234,
					is_followed: false,
					goods: [
						{ id: 7, image: '/static/images/4-001.png', price: '1680.00' },
						{ id: 8, image: '/static/images/4-002.png', price: '90.00' },
						{ id: 9, image: '/static/images/1-001.png', price: '389.00' }
					]
				},
				{
					id: 4,
					name: '豆蔻精品旗舰店',
					logo: '/static/images/def_avatar.png',
					description: '16人关注',
					rating: '4.9',
					sales: 3456,
					goods_count: 567,
					is_followed: false,
					goods: [
						{ id: 10, image: '/static/images/1-002.png', price: '899.00' },
						{ id: 11, image: '/static/images/2-001.png', price: '999.00' },
						{ id: 12, image: '/static/images/2-002.png', price: '22.00' }
					]
				},
				{
					id: 5,
					name: 'CRTNZG旗舰店',
					logo: '/static/images/def_avatar.png',
					description: '15人关注',
					rating: '4.8',
					sales: 1890,
					goods_count: 123,
					is_followed: true,
					goods: [
						{ id: 13, image: '/static/images/3-001.png', price: '79.00' },
						{ id: 14, image: '/static/images/3-002.png', price: '188.00' },
						{ id: 15, image: '/static/images/4-001.png', price: '119.00' }
					]
				},
				{
					id: 6,
					name: '精品数码店',
					logo: '/static/images/def_avatar.png',
					description: '5人关注',
					rating: '5.0',
					sales: 567,
					goods_count: 45,
					is_followed: false,
					goods: [
						{ id: 16, image: '/static/images/4-002.png', price: '2589.00' },
						{ id: 17, image: '/static/images/1-001.png', price: '1899.00' },
						{ id: 18, image: '/static/images/1-002.png', price: '3899.00' }
					]
				}
			];
		}
	}
}
</script>

<style lang="scss" scoped>
.shop-list-container {
	min-height: 100vh;
	background: #F5F5F5;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.navbar-left, .navbar-right {
			width: 80rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.iconfont {
				font-size: 36rpx;
				color: #FFFFFF;
			}
		}

		.navbar-center {
			flex: 1;
			text-align: center;

			.navbar-title {
				font-size: 36rpx;
				font-weight: 600;
				color: #FFFFFF;
			}
		}
	}
}

/* 搜索栏 */
.search-bar {
	position: fixed;
	top: 88rpx;
	left: 0;
	right: 0;
	z-index: 998;
	background: #FFFFFF;
	padding: 20rpx 32rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

	.search-input {
		flex: 1;
		height: 72rpx;
		background: #F8F9FA;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 24rpx;
		margin-right: 20rpx;

		.iconfont {
			font-size: 32rpx;
			color: #CCCCCC;
			margin-right: 16rpx;
		}

		input {
			flex: 1;
			font-size: 28rpx;
			color: #333333;
		}
	}

	.search-cancel {
		font-size: 28rpx;
		color: #FF6B35;
	}
}

/* 筛选栏 */
.filter-bar {
	position: fixed;
	top: 88rpx;
	left: 0;
	right: 0;
	z-index: 997;
	background: #FFFFFF;
	border-bottom: 1rpx solid #F0F0F0;

	.filter-scroll {
		white-space: nowrap;

		.filter-list {
			display: flex;
			padding: 0 32rpx;

			.filter-item {
				flex-shrink: 0;
				padding: 24rpx 32rpx;
				font-size: 28rpx;
				color: #666666;
				position: relative;

				&.active {
					color: #FF6B35;
					font-weight: 600;

					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 40rpx;
						height: 4rpx;
						background: #FF6B35;
						border-radius: 2rpx;
					}
				}
			}
		}
	}
}

/* 店铺列表滚动区域 */
.shop-scroll {
	background: transparent;
}

/* 店铺列表 */
.shop-list {
	padding: 20rpx 32rpx;

	.shop-item {
		background: #FFFFFF;
		border-radius: 20rpx;
		margin-bottom: 24rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

		/* 店铺头部 */
		.shop-header {
			display: flex;
			align-items: flex-start;
			flex-direction: row;
			margin-bottom: 24rpx;

			.shop-logo {
				width: 120rpx;
				height: 120rpx;
				border-radius: 16rpx;
				overflow: hidden;
				margin-right: 24rpx;
				flex-shrink: 0;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.shop-info {
				flex: 1;
				margin-right: 20rpx;

				.shop-name {
					font-size: 32rpx;
					font-weight: 600;
					color: #333333;
					margin-bottom: 8rpx;
				}

				.shop-desc {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 16rpx;
				}

				.shop-stats {
					display: flex;
					align-items: center;

					.stat-item {
						margin-right: 32rpx;
						text-align: center;

						.stat-label {
							display: block;
							font-size: 20rpx;
							color: #999999;
							margin-bottom: 4rpx;
						}

						.stat-value {
							font-size: 24rpx;
							color: #333333;
							font-weight: 600;
						}
					}
				}
			}

			.shop-follow {
				.follow-btn {
					padding: 12rpx 24rpx;
					border: 2rpx solid #FF6B35;
					border-radius: 32rpx;
					font-size: 24rpx;
					color: #FF6B35;
					background: transparent;

					&.followed {
						background: #FF6B35;
						color: #FFFFFF;
					}
				}
			}
		}

		/* 商品预览 */
		.goods-preview {
			display: flex;
			gap: 16rpx;

			.goods-item {
				flex: 1;
				text-align: center;

				image {
					width: 100%;
					height: 160rpx;
					border-radius: 12rpx;
					margin-bottom: 8rpx;
				}

				.goods-price {
					font-size: 24rpx;
					color: #FF6B35;
					font-weight: 600;
				}
			}
		}
	}
}

/* 加载更多 */
.load-more {
	text-align: center;
	padding: 40rpx 0;
	font-size: 28rpx;
	color: #999999;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;

	image {
		width: 300rpx;
		height: 240rpx;
		margin-bottom: 32rpx;
	}

	text {
		font-size: 28rpx;
		color: #999999;
	}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.shop-header {
		flex-direction: column;
		align-items: flex-start !important;

		.shop-logo {
			margin-bottom: 16rpx;
		}

		.shop-info {
			margin-right: 0 !important;
			margin-bottom: 16rpx;
		}
	}

	.goods-preview {
		.goods-item {
			image {
				height: 120rpx !important;
			}
		}
	}
}
</style>
